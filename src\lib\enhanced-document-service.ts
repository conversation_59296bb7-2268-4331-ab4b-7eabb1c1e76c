// Enhanced document service with DeepSeek + Doubao integration
import path from "path";
import documentManager, { type DocumentMetadata } from "./document-manager";
import { DeepSeekService } from "./deepseek-llm";
import { DoubaoEmbeddingService } from "./doubao-embedding";

// Create service instances
const deepSeekService = new DeepSeekService({
  apiKey: process.env.DEEPSEEK_API_KEY || "",
  baseURL: process.env.DEEPSEEK_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
  model: process.env.DEEPSEEK_CHAT_MODEL || "deepseek-chat",
  temperature: 0.7,
  maxTokens: 4000,
});

const doubaoEmbeddingService = new DoubaoEmbeddingService({
  apiKey: process.env.DOUBAO_API_KEY || "",
  baseURL: process.env.DOUBAO_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
  model: process.env.EMBEDDING_MODEL || "doubao-embedding-text-240515",
});

interface DocumentChunk {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    documentId: string;
    filename: string;
    chunkIndex: number;
    startChar: number;
    endChar: number;
  };
}

interface QueryResult {
  response: string;
  sources: Array<{
    document: string;
    chunk: string;
    relevance: number;
  }>;
}

class EnhancedDocumentService {
  private documents: Map<string, { content: string; metadata: DocumentMetadata }> = new Map();
  private documentChunks: Map<string, DocumentChunk[]> = new Map();

  async initialize() {
    try {
      // Initialize document manager
      await documentManager.initialize();
      console.log("Enhanced document service initialized with DeepSeek + Doubao integration");
    } catch (error) {
      console.error("Failed to initialize enhanced document service:", error);
      throw error;
    }
  }

  private chunkText(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    const chunks: string[] = [];
    let start = 0;

    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);
      chunks.push(chunk);
      
      if (end === text.length) break;
      start = end - overlap;
    }

    return chunks;
  }

  async addDocument(filePath: string, documentId: string, filename: string, fileSize: number): Promise<void> {
    try {
      await this.initialize();

      // Update document status to processing
      await documentManager.updateDocumentStatus(documentId, 'processing');

      // Read the document content
      const fs = await import('fs/promises');
      const content = await fs.readFile(filePath, 'utf-8');

      // Store document
      this.documents.set(documentId, {
        content,
        metadata: {
          id: documentId,
          filename,
          originalName: filename,
          size: fileSize,
          uploadDate: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          type: path.extname(filename),
          status: 'processing',
        },
      });

      // Chunk the document
      const textChunks = this.chunkText(content);
      
      // Generate embeddings for each chunk
      const chunks: DocumentChunk[] = [];
      for (let i = 0; i < textChunks.length; i++) {
        const chunkContent = textChunks[i];
        
        try {
          const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);
          
          chunks.push({
            id: `${documentId}_chunk_${i}`,
            content: chunkContent,
            embedding,
            metadata: {
              documentId,
              filename,
              chunkIndex: i,
              startChar: i * 800, // Approximate start position
              endChar: i * 800 + chunkContent.length,
            },
          });
        } catch (embeddingError) {
          console.error(`Failed to generate embedding for chunk ${i}:`, embeddingError);
          // Continue with other chunks even if one fails
        }
      }

      // Store chunks
      this.documentChunks.set(documentId, chunks);

      // Update document status to indexed
      await documentManager.updateDocumentStatus(documentId, 'indexed');

      console.log(`Document ${filename} processed with ${chunks.length} chunks and embeddings`);
    } catch (error) {
      console.error("Failed to add document:", error);
      await documentManager.updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async removeDocument(documentId: string): Promise<void> {
    try {
      await this.initialize();

      // Remove from document manager
      await documentManager.removeDocument(documentId);

      // Remove from our stores
      this.documents.delete(documentId);
      this.documentChunks.delete(documentId);

      console.log(`Document ${documentId} removed from enhanced document store`);
    } catch (error) {
      console.error("Failed to remove document:", error);
      throw error;
    }
  }

  async query(message: string, _chatHistory: Array<{ role: string; content: string }> = []): Promise<QueryResult> {
    try {
      await this.initialize();

      // Check if we have any documents
      if (this.documents.size === 0) {
        return {
          response: "I don't have any documents to search through. Please upload some documents first and wait for them to be processed.",
          sources: [],
        };
      }

      // Generate embedding for the query
      const queryEmbedding = await doubaoEmbeddingService.getEmbedding(message);

      // Get all document chunks with embeddings
      const allChunks: Array<{ embedding: number[]; metadata: any; content: string }> = [];
      for (const [documentId, chunks] of this.documentChunks.entries()) {
        for (const chunk of chunks) {
          allChunks.push({
            embedding: chunk.embedding,
            metadata: {
              documentId,
              filename: chunk.metadata.filename,
              chunkIndex: chunk.metadata.chunkIndex,
            },
            content: chunk.content,
          });
        }
      }

      // Find most similar chunks
      const similarChunks = await doubaoEmbeddingService.findMostSimilar(
        queryEmbedding,
        allChunks,
        5 // Top 5 most relevant chunks
      );

      // Create context from relevant chunks
      const context = similarChunks
        .map((result, index) => {
          const chunk = allChunks.find(c => 
            c.metadata.documentId === result.metadata.documentId && 
            c.metadata.chunkIndex === result.metadata.chunkIndex
          );
          return `[Chunk ${index + 1} from ${result.metadata.filename}]:\n${chunk?.content || 'Content not found'}`;
        })
        .join('\n\n---\n\n');

      // Use DeepSeek to answer the question with context
      const response = await deepSeekService.queryWithContext(message, context);

      // Create sources from similar chunks
      const sources = similarChunks.map((result, index) => {
        const chunk = allChunks.find(c => 
          c.metadata.documentId === result.metadata.documentId && 
          c.metadata.chunkIndex === result.metadata.chunkIndex
        );
        return {
          document: result.metadata.filename,
          chunk: chunk?.content.substring(0, 200) + "..." || "Content not available",
          relevance: Math.round(result.similarity * 100) / 100,
        };
      });

      return {
        response,
        sources,
      };
    } catch (error) {
      console.error("Failed to query with DeepSeek + Doubao:", error);
      throw error;
    }
  }

  async getDocuments(): Promise<DocumentMetadata[]> {
    return Array.from(this.documents.values()).map(doc => doc.metadata);
  }

  async getDocumentStats() {
    const documents = Array.from(this.documents.values());
    const chunks = Array.from(this.documentChunks.values()).flat();
    
    return {
      totalDocuments: documents.length,
      totalChunks: chunks.length,
      totalEmbeddings: chunks.filter(c => c.embedding.length > 0).length,
      averageChunksPerDocument: documents.length > 0 ? Math.round(chunks.length / documents.length) : 0,
    };
  }
}

// Singleton instance
const enhancedDocumentService = new EnhancedDocumentService();

export default enhancedDocumentService;
