/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\code\\\\chatdoc-v1\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_code_chatdoc_v1_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_enhanced_document_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/enhanced-document-service */ \"(rsc)/./src/lib/enhanced-document-service.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if DeepSeek and Doubao API keys are configured\n        if (!process.env.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY === 'your_deepseek_api_key_here') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"⚠️ DeepSeek API key is not configured. Please add your DeepSeek API key to the .env.local file to enable AI-powered responses.\\n\\nTo get started:\\n1. Get an API key from your DeepSeek provider\\n2. Add it to your .env.local file as DEEPSEEK_API_KEY=your_key_here\\n3. Restart the development server\\n\\n🔧 **DeepSeek + Doubao Integration Active** - This application uses DeepSeek for reasoning and Doubao for embeddings!\",\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n        if (!process.env.DOUBAO_API_KEY || process.env.DOUBAO_API_KEY === 'your_doubao_api_key_here') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: \"⚠️ Doubao API key is not configured. Please add your Doubao API key to the .env.local file to enable document embeddings.\\n\\nTo get started:\\n1. Get an API key from ByteDance Volcengine\\n2. Add it to your .env.local file as DOUBAO_API_KEY=your_key_here\\n3. Restart the development server\\n\\n🔧 **Chinese AI Stack** - DeepSeek (reasoning) + Doubao (embeddings) for enhanced document understanding!\",\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n        // Convert chat history to LlamaIndex format\n        const chatHistory = (history || []).map((msg)=>({\n                role: msg.type === 'user' ? 'user' : 'assistant',\n                content: msg.content\n            }));\n        try {\n            // Query using Enhanced Document Service with DeepSeek + Doubao\n            const result = await _lib_enhanced_document_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].query(message, chatHistory);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: result.response,\n                sources: result.sources,\n                timestamp: new Date().toISOString()\n            });\n        } catch (queryError) {\n            console.error('DeepSeek + Doubao query error:', queryError);\n            // Fallback response if the enhanced service fails\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: `I apologize, but I encountered an error while processing your question: \"${message}\".\n\n**Possible issues:**\n- DeepSeek or Doubao API keys might be invalid or expired\n- No documents have been uploaded yet\n- Network connectivity issues\n- Embedding generation failed\n\n**🔧 Chinese AI Stack Status:**\n- ✅ DeepSeek API configured (reasoning)\n- ✅ Doubao API configured (embeddings)\n- ⚠️ Error occurred during query processing\n\nPlease check your API keys and ensure you have uploaded some documents, then try again.\n\n**Error details:** ${queryError instanceof Error ? queryError.message : 'Unknown error'}`,\n                sources: [],\n                timestamp: new Date().toISOString()\n            });\n        }\n    } catch (error) {\n        console.error('Chat error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat message'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/deepseek-llm.ts":
/*!*********************************!*\
  !*** ./src/lib/deepseek-llm.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeepSeekService: () => (/* binding */ DeepSeekService)\n/* harmony export */ });\nclass DeepSeekService {\n    constructor(config){\n        this.config = {\n            temperature: 0.7,\n            maxTokens: 4000,\n            ...config\n        };\n    }\n    async chat(messages) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    messages: messages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        })),\n                    temperature: this.config.temperature,\n                    max_tokens: this.config.maxTokens,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('No response from DeepSeek API');\n            }\n            const choice = data.choices[0];\n            return {\n                message: {\n                    role: choice.message.role,\n                    content: choice.message.content\n                },\n                raw: data\n            };\n        } catch (error) {\n            console.error('DeepSeek LLM error:', error);\n            throw error;\n        }\n    }\n    async queryWithContext(message, context) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are a helpful AI assistant that answers questions based on the provided document context. Use the context to provide accurate and relevant answers. If the context doesn't contain enough information to answer the question, say so clearly.\n\nContext:\n${context}`\n            },\n            {\n                role: 'user',\n                content: message\n            }\n        ];\n        const response = await this.chat(messages);\n        return response.message.content;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/deepseek-llm.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/document-manager.ts":
/*!*************************************!*\
  !*** ./src/lib/document-manager.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass DocumentManager {\n    async initialize() {\n        try {\n            if ((0,fs__WEBPACK_IMPORTED_MODULE_1__.existsSync)(this.metadataFile)) {\n                const data = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile)(this.metadataFile, 'utf-8');\n                const documentsArray = JSON.parse(data);\n                this.documents = new Map(documentsArray.map((doc)=>[\n                        doc.id,\n                        doc\n                    ]));\n                console.log(`Loaded ${this.documents.size} document metadata entries`);\n            }\n        } catch (error) {\n            console.error('Failed to load document metadata:', error);\n        }\n    }\n    async addDocument(metadata) {\n        const fullMetadata = {\n            ...metadata,\n            uploadDate: new Date().toISOString(),\n            lastModified: new Date().toISOString(),\n            status: 'uploading'\n        };\n        this.documents.set(metadata.id, fullMetadata);\n        await this.saveMetadata();\n        return fullMetadata;\n    }\n    async updateDocumentStatus(id, status, errorMessage) {\n        const doc = this.documents.get(id);\n        if (doc) {\n            doc.status = status;\n            doc.lastModified = new Date().toISOString();\n            if (errorMessage) {\n                doc.errorMessage = errorMessage;\n            }\n            this.documents.set(id, doc);\n            await this.saveMetadata();\n        }\n    }\n    async removeDocument(id) {\n        this.documents.delete(id);\n        await this.saveMetadata();\n    }\n    getDocument(id) {\n        return this.documents.get(id);\n    }\n    getAllDocuments() {\n        return Array.from(this.documents.values());\n    }\n    getDocumentsByStatus(status) {\n        return Array.from(this.documents.values()).filter((doc)=>doc.status === status);\n    }\n    async saveMetadata() {\n        try {\n            const documentsArray = Array.from(this.documents.values());\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile)(this.metadataFile, JSON.stringify(documentsArray, null, 2));\n        } catch (error) {\n            console.error('Failed to save document metadata:', error);\n        }\n    }\n    async getStats() {\n        const docs = Array.from(this.documents.values());\n        return {\n            total: docs.length,\n            indexed: docs.filter((d)=>d.status === 'indexed').length,\n            processing: docs.filter((d)=>d.status === 'processing').length,\n            errors: docs.filter((d)=>d.status === 'error').length,\n            totalSize: docs.reduce((sum, doc)=>sum + doc.size, 0)\n        };\n    }\n    constructor(){\n        this.metadataFile = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'documents_metadata.json');\n        this.documents = new Map();\n    }\n}\n// Singleton instance\nconst documentManager = new DocumentManager();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/document-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/doubao-embedding.ts":
/*!*************************************!*\
  !*** ./src/lib/doubao-embedding.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoubaoEmbeddingService: () => (/* binding */ DoubaoEmbeddingService)\n/* harmony export */ });\nclass DoubaoEmbeddingService {\n    constructor(config){\n        this.config = config;\n    }\n    async getEmbedding(text) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/embeddings`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    input: text,\n                    encoding_format: 'float'\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.data || data.data.length === 0) {\n                throw new Error('No embedding data returned from Doubao API');\n            }\n            return data.data[0].embedding;\n        } catch (error) {\n            console.error('Doubao embedding error:', error);\n            throw error;\n        }\n    }\n    async getEmbeddings(texts) {\n        try {\n            const response = await fetch(`${this.config.baseURL}/embeddings`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.config.apiKey}`\n                },\n                body: JSON.stringify({\n                    model: this.config.model,\n                    input: texts,\n                    encoding_format: 'float'\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`Doubao Embedding API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.data || data.data.length === 0) {\n                throw new Error('No embedding data returned from Doubao API');\n            }\n            // Sort by index to maintain order\n            return data.data.sort((a, b)=>a.index - b.index).map((item)=>item.embedding);\n        } catch (error) {\n            console.error('Doubao embeddings error:', error);\n            throw error;\n        }\n    }\n    async calculateSimilarity(embedding1, embedding2) {\n        if (embedding1.length !== embedding2.length) {\n            throw new Error('Embeddings must have the same dimension');\n        }\n        // Calculate cosine similarity\n        let dotProduct = 0;\n        let norm1 = 0;\n        let norm2 = 0;\n        for(let i = 0; i < embedding1.length; i++){\n            dotProduct += embedding1[i] * embedding2[i];\n            norm1 += embedding1[i] * embedding1[i];\n            norm2 += embedding2[i] * embedding2[i];\n        }\n        const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);\n        return magnitude === 0 ? 0 : dotProduct / magnitude;\n    }\n    async findMostSimilar(queryEmbedding, documentEmbeddings, topK = 5) {\n        const similarities = await Promise.all(documentEmbeddings.map(async (doc)=>({\n                similarity: await this.calculateSimilarity(queryEmbedding, doc.embedding),\n                metadata: doc.metadata\n            })));\n        return similarities.sort((a, b)=>b.similarity - a.similarity).slice(0, topK);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/doubao-embedding.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/enhanced-document-service.ts":
/*!**********************************************!*\
  !*** ./src/lib/enhanced-document-service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _document_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./document-manager */ \"(rsc)/./src/lib/document-manager.ts\");\n/* harmony import */ var _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./deepseek-llm */ \"(rsc)/./src/lib/deepseek-llm.ts\");\n/* harmony import */ var _doubao_embedding__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./doubao-embedding */ \"(rsc)/./src/lib/doubao-embedding.ts\");\n// Enhanced document service with DeepSeek + Doubao integration\n\n\n\n\n// Create service instances\nconst deepSeekService = new _deepseek_llm__WEBPACK_IMPORTED_MODULE_2__.DeepSeekService({\n    apiKey: process.env.DEEPSEEK_API_KEY || \"\",\n    baseURL: process.env.DEEPSEEK_BASE_URL || \"https://ark.cn-beijing.volces.com/api/v3\",\n    model: process.env.DEEPSEEK_CHAT_MODEL || \"deepseek-chat\",\n    temperature: 0.7,\n    maxTokens: 4000\n});\nconst doubaoEmbeddingService = new _doubao_embedding__WEBPACK_IMPORTED_MODULE_3__.DoubaoEmbeddingService({\n    apiKey: process.env.DOUBAO_API_KEY || \"\",\n    baseURL: process.env.DOUBAO_BASE_URL || \"https://ark.cn-beijing.volces.com/api/v3\",\n    model: process.env.EMBEDDING_MODEL || \"doubao-embedding-text-240515\"\n});\nclass EnhancedDocumentService {\n    async initialize() {\n        try {\n            // Initialize document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].initialize();\n            console.log(\"Enhanced document service initialized with DeepSeek + Doubao integration\");\n        } catch (error) {\n            console.error(\"Failed to initialize enhanced document service:\", error);\n            throw error;\n        }\n    }\n    chunkText(text, chunkSize = 1000, overlap = 200) {\n        const chunks = [];\n        let start = 0;\n        while(start < text.length){\n            const end = Math.min(start + chunkSize, text.length);\n            const chunk = text.slice(start, end);\n            chunks.push(chunk);\n            if (end === text.length) break;\n            start = end - overlap;\n        }\n        return chunks;\n    }\n    async addDocument(filePath, documentId, filename, fileSize) {\n        try {\n            await this.initialize();\n            // Update document status to processing\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'processing');\n            // Read the document content\n            const fs = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! fs/promises */ \"fs/promises\", 23));\n            const content = await fs.readFile(filePath, 'utf-8');\n            // Store document\n            this.documents.set(documentId, {\n                content,\n                metadata: {\n                    id: documentId,\n                    filename,\n                    originalName: filename,\n                    size: fileSize,\n                    uploadDate: new Date().toISOString(),\n                    lastModified: new Date().toISOString(),\n                    type: path__WEBPACK_IMPORTED_MODULE_0___default().extname(filename),\n                    status: 'processing'\n                }\n            });\n            // Chunk the document\n            const textChunks = this.chunkText(content);\n            // Generate embeddings for each chunk\n            const chunks = [];\n            for(let i = 0; i < textChunks.length; i++){\n                const chunkContent = textChunks[i];\n                try {\n                    const embedding = await doubaoEmbeddingService.getEmbedding(chunkContent);\n                    chunks.push({\n                        id: `${documentId}_chunk_${i}`,\n                        content: chunkContent,\n                        embedding,\n                        metadata: {\n                            documentId,\n                            filename,\n                            chunkIndex: i,\n                            startChar: i * 800,\n                            endChar: i * 800 + chunkContent.length\n                        }\n                    });\n                } catch (embeddingError) {\n                    console.error(`Failed to generate embedding for chunk ${i}:`, embeddingError);\n                // Continue with other chunks even if one fails\n                }\n            }\n            // Store chunks\n            this.documentChunks.set(documentId, chunks);\n            // Update document status to indexed\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'indexed');\n            console.log(`Document ${filename} processed with ${chunks.length} chunks and embeddings`);\n        } catch (error) {\n            console.error(\"Failed to add document:\", error);\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');\n            throw error;\n        }\n    }\n    async removeDocument(documentId) {\n        try {\n            await this.initialize();\n            // Remove from document manager\n            await _document_manager__WEBPACK_IMPORTED_MODULE_1__[\"default\"].removeDocument(documentId);\n            // Remove from our stores\n            this.documents.delete(documentId);\n            this.documentChunks.delete(documentId);\n            console.log(`Document ${documentId} removed from enhanced document store`);\n        } catch (error) {\n            console.error(\"Failed to remove document:\", error);\n            throw error;\n        }\n    }\n    async query(message, _chatHistory = []) {\n        try {\n            await this.initialize();\n            // Check if we have any documents\n            if (this.documents.size === 0) {\n                return {\n                    response: \"I don't have any documents to search through. Please upload some documents first and wait for them to be processed.\",\n                    sources: []\n                };\n            }\n            // Generate embedding for the query\n            const queryEmbedding = await doubaoEmbeddingService.getEmbedding(message);\n            // Get all document chunks with embeddings\n            const allChunks = [];\n            for (const [documentId, chunks] of this.documentChunks.entries()){\n                for (const chunk of chunks){\n                    allChunks.push({\n                        embedding: chunk.embedding,\n                        metadata: {\n                            documentId,\n                            filename: chunk.metadata.filename,\n                            chunkIndex: chunk.metadata.chunkIndex\n                        },\n                        content: chunk.content\n                    });\n                }\n            }\n            // Find most similar chunks\n            const similarChunks = await doubaoEmbeddingService.findMostSimilar(queryEmbedding, allChunks, 5 // Top 5 most relevant chunks\n            );\n            // Create context from relevant chunks\n            const context = similarChunks.map((result, index)=>{\n                const chunk = allChunks.find((c)=>c.metadata.documentId === result.metadata.documentId && c.metadata.chunkIndex === result.metadata.chunkIndex);\n                return `[Chunk ${index + 1} from ${result.metadata.filename}]:\\n${chunk?.content || 'Content not found'}`;\n            }).join('\\n\\n---\\n\\n');\n            // Use DeepSeek to answer the question with context\n            const response = await deepSeekService.queryWithContext(message, context);\n            // Create sources from similar chunks\n            const sources = similarChunks.map((result, index)=>{\n                const chunk = allChunks.find((c)=>c.metadata.documentId === result.metadata.documentId && c.metadata.chunkIndex === result.metadata.chunkIndex);\n                return {\n                    document: result.metadata.filename,\n                    chunk: chunk?.content.substring(0, 200) + \"...\" || 0,\n                    relevance: Math.round(result.similarity * 100) / 100\n                };\n            });\n            return {\n                response,\n                sources\n            };\n        } catch (error) {\n            console.error(\"Failed to query with DeepSeek + Doubao:\", error);\n            throw error;\n        }\n    }\n    async getDocuments() {\n        return Array.from(this.documents.values()).map((doc)=>doc.metadata);\n    }\n    async getDocumentStats() {\n        const documents = Array.from(this.documents.values());\n        const chunks = Array.from(this.documentChunks.values()).flat();\n        return {\n            totalDocuments: documents.length,\n            totalChunks: chunks.length,\n            totalEmbeddings: chunks.filter((c)=>c.embedding.length > 0).length,\n            averageChunksPerDocument: documents.length > 0 ? Math.round(chunks.length / documents.length) : 0\n        };\n    }\n    constructor(){\n        this.documents = new Map();\n        this.documentChunks = new Map();\n    }\n}\n// Singleton instance\nconst enhancedDocumentService = new EnhancedDocumentService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (enhancedDocumentService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/enhanced-document-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Ccode%5Cchatdoc-v1%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode%5Cchatdoc-v1&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();